import React from 'react';
import { PackageList } from '../components/packages/PackageList';

import { TrendingUp, Shield, Zap } from 'lucide-react';

export function HomePage() {
  return (
    <div className="space-y-12">
      {/* Hero Section */}
      <div className="text-center space-y-6">
        <div className="space-y-4">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 leading-tight">
            Invest in the Future of
            <span className="text-primary-600 block">Decentralized Finance</span>
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            BlockCoop offers sophisticated investment packages with advanced splitting mechanisms, 
            vesting schedules, and secondary market integration on BSC.
          </p>
        </div>
        
        <div className="flex flex-wrap justify-center gap-8 text-sm text-gray-600">
          <div className="flex items-center space-x-2">
            <TrendingUp className="h-5 w-5 text-primary-600" />
            <span>Dynamic Splits</span>
          </div>
          <div className="flex items-center space-x-2">
            <Shield className="h-5 w-5 text-accent-600" />
            <span>Secure Vesting</span>
          </div>
          <div className="flex items-center space-x-2">
            <Zap className="h-5 w-5 text-warning-500" />
            <span>Instant Liquidity</span>
          </div>
        </div>
      </div>

      {/* Features Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div className="text-center space-y-4">
          <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto">
            <TrendingUp className="h-8 w-8 text-primary-600" />
          </div>
          <h3 className="text-xl font-semibold text-gray-900">Smart Splitting</h3>
          <p className="text-gray-600">
            Advanced algorithms automatically split your investment between LP pools and vesting vaults for optimal returns.
          </p>
        </div>
        
        <div className="text-center space-y-4">
          <div className="w-16 h-16 bg-accent-100 rounded-full flex items-center justify-center mx-auto">
            <Shield className="h-8 w-8 text-accent-600" />
          </div>
          <h3 className="text-xl font-semibold text-gray-900">Secure Vesting</h3>
          <p className="text-gray-600">
            Time-locked vesting schedules protect your long-term interests while providing predictable token release.
          </p>
        </div>
        
        <div className="text-center space-y-4">
          <div className="w-16 h-16 bg-warning-100 rounded-full flex items-center justify-center mx-auto">
            <Zap className="h-8 w-8 text-warning-600" />
          </div>
          <h3 className="text-xl font-semibold text-gray-900">Instant Liquidity</h3>
          <p className="text-gray-600">
            Redeem your LP tokens anytime or trade on secondary markets for maximum flexibility.
          </p>
        </div>
      </div>

      {/* Packages Section */}
      <div className="space-y-8">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Investment Packages</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Choose from our carefully crafted investment packages, each designed with unique split ratios and vesting schedules.
          </p>
        </div>
        
        <PackageList />
      </div>


    </div>
  );
}
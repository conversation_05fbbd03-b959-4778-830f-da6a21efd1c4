import { useState, useCallback, useEffect } from 'react';
import { ethers } from 'ethers';
import { useWeb3 } from '../providers/Web3Provider';
import { useRefreshListener } from '../contexts/RefreshContext';
import {
  getLiquidityRemovalPreview,
  redeemLPTokensWithLiquidityRemoval,
  getTokenPricesFromPool,
} from '../lib/contracts';
import {
  LiquidityRemovalPreview,
  SlippageConfig,
  MEVProtection,
  DEFAULT_SLIPPAGE_CONFIG,
  DEFAULT_MEV_PROTECTION,
} from '../lib/liquidityManager';

export interface LiquidityRedemptionState {
  preview: LiquidityRemovalPreview | null;
  tokenPrices: { shareTokenPriceInUSDT: number; usdtPriceInShareToken: number } | null;
  loading: boolean;
  error: string | null;
}

export interface LiquidityRedemptionConfig {
  slippage: SlippageConfig;
  mevProtection: MEVProtection;
}

/**
 * Enhanced hook for LP token redemption with actual liquidity removal
 */
export function useLiquidityRedemption() {
  const { signer, isConnected } = useWeb3();
  const [state, setState] = useState<LiquidityRedemptionState>({
    preview: null,
    tokenPrices: null,
    loading: false,
    error: null,
  });

  const [config, setConfig] = useState<LiquidityRedemptionConfig>({
    slippage: DEFAULT_SLIPPAGE_CONFIG,
    mevProtection: DEFAULT_MEV_PROTECTION,
  });

  // Update slippage configuration
  const updateSlippageConfig = useCallback((newConfig: Partial<SlippageConfig>) => {
    setConfig(prev => ({
      ...prev,
      slippage: { ...prev.slippage, ...newConfig },
    }));
  }, []);

  // Update MEV protection configuration
  const updateMEVProtection = useCallback((newConfig: Partial<MEVProtection>) => {
    setConfig(prev => ({
      ...prev,
      mevProtection: { ...prev.mevProtection, ...newConfig },
    }));
  }, []);

  // Get liquidity removal preview
  const getPreview = useCallback(async (lpAmount: bigint): Promise<LiquidityRemovalPreview | null> => {
    if (!signer || !isConnected || lpAmount <= 0n) {
      return null;
    }

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const preview = await getLiquidityRemovalPreview(lpAmount, signer, config.slippage);
      setState(prev => ({ ...prev, preview, loading: false }));
      return preview;
    } catch (error: any) {
      console.error('Failed to get liquidity removal preview:', error);
      setState(prev => ({
        ...prev,
        error: error.message || 'Failed to get preview',
        loading: false,
      }));
      return null;
    }
  }, [signer, isConnected, config.slippage]);

  // Execute liquidity removal
  const executeLiquidityRemoval = useCallback(async (lpAmount: bigint) => {
    if (!signer || !isConnected) {
      throw new Error('Wallet not connected');
    }

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const tx = await redeemLPTokensWithLiquidityRemoval(
        lpAmount,
        signer,
        config.slippage,
        config.mevProtection
      );

      setState(prev => ({ ...prev, loading: false }));
      return tx;
    } catch (error: any) {
      console.error('Failed to execute liquidity removal:', error);
      setState(prev => ({
        ...prev,
        error: error.message || 'Failed to remove liquidity',
        loading: false,
      }));
      throw error;
    }
  }, [signer, isConnected, config]);

  // Get current token prices
  const fetchTokenPrices = useCallback(async () => {
    if (!signer || !isConnected) {
      return;
    }

    try {
      const prices = await getTokenPricesFromPool(signer);
      setState(prev => ({ ...prev, tokenPrices: prices }));
    } catch (error: any) {
      console.error('Failed to fetch token prices:', error);
      // Don't set error state for price fetching as it's not critical
    }
  }, [signer, isConnected]);

  // Clear preview
  const clearPreview = useCallback(() => {
    setState(prev => ({ ...prev, preview: null, error: null }));
  }, []);

  // Clear error
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  // Auto-refresh token prices
  useRefreshListener('refreshTokenPrices', fetchTokenPrices);

  // Fetch token prices when connected
  useEffect(() => {
    if (isConnected && signer) {
      fetchTokenPrices();
    }
  }, [isConnected, signer, fetchTokenPrices]);

  return {
    // State
    preview: state.preview,
    tokenPrices: state.tokenPrices,
    loading: state.loading,
    error: state.error,
    config,

    // Actions
    getPreview,
    executeLiquidityRemoval,
    fetchTokenPrices,
    clearPreview,
    clearError,
    updateSlippageConfig,
    updateMEVProtection,

    // Computed values
    hasPreview: state.preview !== null,
    canExecute: state.preview !== null && !state.loading && !state.error,
  };
}

/**
 * Hook for slippage tolerance management
 */
export function useSlippageTolerance(initialTolerance: number = 0.5) {
  const [tolerance, setTolerance] = useState(initialTolerance);

  const updateTolerance = useCallback((newTolerance: number) => {
    // Validate tolerance range (0.1% to 50%)
    const clampedTolerance = Math.max(0.1, Math.min(50, newTolerance));
    setTolerance(clampedTolerance);
  }, []);

  const getSlippageConfig = useCallback((deadline: number = 300): SlippageConfig => ({
    tolerance,
    deadline,
  }), [tolerance]);

  return {
    tolerance,
    updateTolerance,
    getSlippageConfig,
    isHighSlippage: tolerance > 5,
    isVeryHighSlippage: tolerance > 15,
  };
}

/**
 * Hook for MEV protection settings
 */
export function useMEVProtection() {
  const [protection, setProtection] = useState<MEVProtection>(DEFAULT_MEV_PROTECTION);

  const updateProtection = useCallback((updates: Partial<MEVProtection>) => {
    setProtection(prev => ({ ...prev, ...updates }));
  }, []);

  const enablePrivateMempool = useCallback(() => {
    setProtection(prev => ({ ...prev, usePrivateMempool: true }));
  }, []);

  const disablePrivateMempool = useCallback(() => {
    setProtection(prev => ({ ...prev, usePrivateMempool: false }));
  }, []);

  return {
    protection,
    updateProtection,
    enablePrivateMempool,
    disablePrivateMempool,
    isProtected: protection.usePrivateMempool,
  };
}

/**
 * Utility hook for formatting liquidity removal data
 */
export function useLiquidityFormatting() {
  const formatTokenAmount = useCallback((amount: bigint, decimals: number = 18): string => {
    return parseFloat(ethers.formatUnits(amount, decimals)).toFixed(6);
  }, []);

  const formatUSDTAmount = useCallback((amount: bigint): string => {
    return parseFloat(ethers.formatUnits(amount, 6)).toFixed(2);
  }, []);

  const formatPercentage = useCallback((value: number): string => {
    return `${value.toFixed(2)}%`;
  }, []);

  const formatPriceImpact = useCallback((impact: number): { text: string; color: string } => {
    const text = formatPercentage(impact);
    let color = 'text-green-600';
    
    if (impact > 0.1) color = 'text-yellow-600';
    if (impact > 1) color = 'text-orange-600';
    if (impact > 3) color = 'text-red-600';
    
    return { text, color };
  }, [formatPercentage]);

  return {
    formatTokenAmount,
    formatUSDTAmount,
    formatPercentage,
    formatPriceImpact,
  };
}

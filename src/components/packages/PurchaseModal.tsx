import { useState, useEffect } from 'react';
import { Dialog } from '@headlessui/react';
import { useWeb3 } from '../../providers/Web3Provider';
import { isAddress, ZeroAddress, MaxUint256, ethers } from 'ethers';
import { Package, calculateSplits } from '../../lib/contracts';
import { formatUSDT, formatPercentage } from '../../lib/utils';
import { getTransactionUrl, executeTransaction } from '../../lib/transactionErrors';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Card, CardContent } from '../ui/Card';
import { X, Info, TrendingUp, ExternalLink, Copy } from 'lucide-react';
import toast from 'react-hot-toast';

interface PurchaseModalProps {
  package: Package;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export function PurchaseModal({ package: pkg, isOpen, onClose, onSuccess }: PurchaseModalProps) {
  const { account: address, signer, isConnected, isCorrectNetwork, contracts } = useWeb3();
  const [referrer, setReferrer] = useState('');
  const [loading, setLoading] = useState(false);
  const [usdtBalance, setUsdtBalance] = useState<bigint>(0n);
  const [allowance, setAllowance] = useState<bigint>(0n);
  const [usdtDecimals, setUsdtDecimals] = useState<number>(6); // Default to 6, will be updated from contract

  const splits = calculateSplits(pkg);

  // CRITICAL FIX: Detect if this is a legacy package (18 decimals) or new package (6 decimals)
  // Legacy packages have very large values (>1e15), new packages have smaller values
  const isLegacyPackage = pkg.entryUSDT > 1000000000000000n; // > 1e15

  // For legacy packages, the contract will try to transfer the 18-decimal amount
  // For new packages, the contract will try to transfer the 6-decimal amount (which is correct)
  const packageCostForContract = pkg.entryUSDT; // Exact amount contract will try to transfer
  const needsApproval = allowance < packageCostForContract;

  // For balance comparison, always use actual USDT token decimals (6)
  const packageCostUSDT = isLegacyPackage
    ? pkg.entryUSDT / (10n ** 12n) // Convert 18 decimals to 6 decimals
    : pkg.entryUSDT; // Already in 6 decimals

  // Helper function to format USDT amounts with 4 decimal places using actual USDT decimals
  const formatTokenDisplay = (amount: bigint): string => {
    try {
      const formatted = ethers.formatUnits(amount, usdtDecimals);
      const num = parseFloat(formatted);
      return num.toLocaleString('en-US', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 4,
      });
    } catch {
      return '0';
    }
  };

  // Utility function to create enhanced transaction success notification
  const createTransactionSuccessToast = (message: string, txHash: string) => {
    const explorerUrl = getTransactionUrl(txHash);
    const shortHash = `${txHash.slice(0, 6)}...${txHash.slice(-4)}`;

    const copyToClipboard = () => {
      navigator.clipboard.writeText(txHash);
      toast.success('Transaction hash copied!', { duration: 2000 });
    };

    return (
      <div className="flex items-center justify-between space-x-3 min-w-0">
        <span className="flex-shrink-0">{message}</span>
        <div className="flex items-center space-x-2 min-w-0">
          <span className="text-xs text-gray-500 font-mono truncate">{shortHash}</span>
          <button
            onClick={copyToClipboard}
            className="text-blue-500 hover:text-blue-700 flex-shrink-0"
            title="Copy transaction hash"
          >
            <Copy className="h-3 w-3" />
          </button>
          <a
            href={explorerUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-500 hover:text-blue-700 flex-shrink-0"
            title="View on block explorer"
          >
            <ExternalLink className="h-3 w-3" />
          </a>
        </div>
      </div>
    );
  };

  useEffect(() => {
    let mounted = true;

    console.log('PurchaseModal: Address/isOpen/contracts changed:', {
      address,
      isOpen,
      isConnected,
      signer: !!signer,
      hasContracts: !!contracts,
      hasUsdtToken: !!contracts?.usdtToken,
      hasPackageManager: !!contracts?.packageManager,
      contractsFromWeb3: contracts,
      usdtTokenAddress: contracts?.usdtToken?.address,
      packageManagerAddress: contracts?.packageManager?.address,
      usdtTokenTarget: contracts?.usdtToken?.target,
      packageManagerTarget: contracts?.packageManager?.target
    });

    if (address && isOpen && contracts?.usdtToken && contracts?.packageManager) {
      console.log('PurchaseModal: Loading balances for address:', address);
      loadBalances(contracts, mounted);
    } else {
      console.log('PurchaseModal: Skipping loadBalances - requirements not met:', {
        hasAddress: !!address,
        isOpen,
        hasContracts: !!contracts,
        hasUsdtToken: !!contracts?.usdtToken,
        hasPackageManager: !!contracts?.packageManager
      });
    }

    return () => {
      mounted = false;
    };
  }, [address, isOpen, contracts]);

  // Reset form state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setReferrer('');
      setLoading(false);
    }
  }, [isOpen]);

  const loadBalances = async (contractInstances: any, mounted = true) => {
    try {
      // Validate that address is available before making contract calls
      if (!address) {
        console.warn('loadBalances called with null/undefined address');
        return;
      }

      // Validate that contracts are properly initialized
      console.log('loadBalances: Contract validation debug:', {
        contractInstances,
        hasUsdtToken: !!contractInstances?.usdtToken,
        hasPackageManager: !!contractInstances?.packageManager,
        usdtTokenType: typeof contractInstances?.usdtToken,
        packageManagerType: typeof contractInstances?.packageManager,
        usdtTokenKeys: contractInstances?.usdtToken ? Object.getOwnPropertyNames(contractInstances.usdtToken) : [],
        packageManagerKeys: contractInstances?.packageManager ? Object.getOwnPropertyNames(contractInstances.packageManager) : [],
        usdtTokenAddress: contractInstances?.usdtToken?.address,
        usdtTokenTarget: contractInstances?.usdtToken?.target,
        packageManagerAddress: contractInstances?.packageManager?.address,
        packageManagerTarget: contractInstances?.packageManager?.target
      });

      if (!contractInstances?.usdtToken || !contractInstances?.packageManager) {
        console.error('loadBalances called with invalid contracts');
        return;
      }

      // Check if addresses are available (Ethers v6 uses .target property)
      const usdtAddress = contractInstances.usdtToken.target || contractInstances.usdtToken.address;
      const packageManagerAddress = contractInstances.packageManager.target || contractInstances.packageManager.address;

      if (!usdtAddress || !packageManagerAddress) {
        console.error('Contract addresses are undefined:', {
          usdtAddress,
          packageManagerAddress,
          usdtTokenTarget: contractInstances.usdtToken.target,
          usdtTokenAddress: contractInstances.usdtToken.address,
          packageManagerTarget: contractInstances.packageManager.target,
          packageManagerAddressProperty: contractInstances.packageManager.address,
          usdtTokenContract: contractInstances.usdtToken,
          packageManagerContract: contractInstances.packageManager
        });
        return;
      }

      console.log('loadBalances: Making contract calls with:', {
        address,
        usdtTokenAddress: usdtAddress,
        packageManagerAddress: packageManagerAddress
      });

      const [balance, currentAllowance, usdtDecimals] = await Promise.all([
        contractInstances.usdtToken.balanceOf(address),
        contractInstances.usdtToken.allowance(address, packageManagerAddress),
        contractInstances.usdtToken.decimals(),
      ]);

      console.log('loadBalances: Contract calls successful:', {
        balance: balance.toString(),
        balanceFormatted6: ethers.formatUnits(balance, 6),
        balanceFormatted18: ethers.formatUnits(balance, 18),
        allowance: currentAllowance.toString(),
        allowanceFormatted6: ethers.formatUnits(currentAllowance, 6),
        allowanceFormatted18: ethers.formatUnits(currentAllowance, 18),
        usdtDecimals: usdtDecimals.toString(),
        balanceFormattedActual: ethers.formatUnits(balance, usdtDecimals),
        allowanceFormattedActual: ethers.formatUnits(currentAllowance, usdtDecimals)
      });

      // Only update state if component is still mounted
      if (mounted) {
        console.log('loadBalances: Updating state with balance:', balance.toString());
        setUsdtBalance(balance);
        setAllowance(currentAllowance);
        setUsdtDecimals(Number(usdtDecimals));
        console.log('loadBalances: State update calls completed');
      }
    } catch (error) {
      console.error('Error loading balances:', error);
      console.error('Address at time of error:', address);
      console.error('Contracts at time of error:', {
        usdtToken: contractInstances?.usdtToken,
        packageManager: contractInstances?.packageManager
      });
    }
  };

  const handleApprove = async () => {
    if (!address) {
      toast.error('Please connect your wallet first');
      return;
    }

    if (!isCorrectNetwork) {
      toast.error('Please switch to BSC Testnet');
      return;
    }

    // Execute approval transaction with enhanced error handling
    const result = await executeTransaction(
      async () => {
        if (!isConnected) throw new Error('Please connect your wallet first');
        if (!signer) throw new Error('No signer available - please ensure your wallet is connected');
        if (!contracts?.usdtToken || !contracts?.packageManager) throw new Error('Contracts not properly initialized');

        const packageManagerAddress = contracts.packageManager.target || contracts.packageManager.address;
        console.log('handleApprove: Approving USDT for PackageManager:', {
          usdtTokenAddress: contracts.usdtToken.target || contracts.usdtToken.address,
          packageManagerAddress,
          approvalAmount: MaxUint256.toString(),
          userAddress: address
        });

        const tx = await contracts.usdtToken.approve(
          packageManagerAddress,
          MaxUint256
        );

        // Wait for transaction confirmation
        const receipt = await tx.wait();
        return { tx, receipt };
      },
      {
        onStart: () => {
          setLoading(true);
          toast.loading('Approving USDT...', { id: 'approve' });
        },
        onSuccess: async ({ tx }) => {
          const txHash = tx.hash;

          // Show enhanced success message with transaction details
          toast.success(
            createTransactionSuccessToast('USDT approved successfully!', txHash),
            { id: 'approve', duration: 8000 }
          );

          // Reload balances to update allowance and re-verify approval
          if (address && contracts?.usdtToken && contracts?.packageManager) {
            await loadBalances(contracts);

            // Re-verify that allowance is sufficient after approval
            try {
              const packageManagerAddress = contracts.packageManager.target || contracts.packageManager.address;
              const newAllowance = await contracts.usdtToken.allowance(address, packageManagerAddress);
              console.log('Approval verification:', {
                userAddress: address,
                packageManagerAddress,
                newAllowance: newAllowance.toString(),
                newAllowanceFormatted: ethers.formatUnits(newAllowance, usdtDecimals),
                packageCostForContract: packageCostForContract.toString(),
                packageCostForContractFormatted: ethers.formatUnits(packageCostForContract, 18),
                packageCostUSDT: packageCostUSDT.toString(),
                packageCostUSDTFormatted: ethers.formatUnits(packageCostUSDT, usdtDecimals),
                isAllowanceSufficient: newAllowance >= packageCostForContract,
                contractWillTryToTransfer: packageCostForContract.toString() + ' (18 decimals)',
                actualUSDTAmount: packageCostUSDT.toString() + ' (6 decimals)'
              });

              if (newAllowance >= packageCostForContract) {
                toast.success('Approval verified - you can now purchase the package!', { duration: 3000 });
              } else {
                toast.error('Approval completed but allowance may be insufficient. Please try again.', { duration: 5000 });
              }
            } catch (error) {
              console.error('Error re-verifying allowance:', error);
            }
          }
        },
        onError: (error) => {
          console.error('Approval error:', error);

          // Show user-friendly error message
          if (error.isUserRejection) {
            toast.error('Approval cancelled', { id: 'approve' });
          } else if (error.isInsufficientFunds) {
            toast.error('Insufficient funds for gas fee', { id: 'approve' });
          } else if (error.isNetworkError) {
            toast.error('Network error. Please check your connection and try again', { id: 'approve' });
          } else {
            toast.error(error.userMessage, { id: 'approve' });
          }
        },
        onFinally: () => {
          setLoading(false);
        }
      }
    );

    return result.success;
  };

  const handlePurchase = async () => {
    // Validate required parameters
    if (!pkg?.id && pkg?.id !== 0) {
      toast.error('Invalid package ID');
      return;
    }

    if (!address) {
      toast.error('Please connect your wallet first');
      return;
    }

    if (!isCorrectNetwork) {
      toast.error('Please switch to BSC Testnet');
      return;
    }

    // Validate referrer address if provided
    if (referrer.trim() && !isAddress(referrer.trim())) {
      toast.error('Invalid referrer address');
      return;
    }
    const referrerAddress = referrer.trim() || ZeroAddress;

    // Execute transaction with enhanced error handling
    const result = await executeTransaction(
      async () => {
        if (!isConnected) throw new Error('Please connect your wallet first');
        if (!signer) throw new Error('No signer available - please ensure your wallet is connected');
        if (!contracts?.packageManager) throw new Error('Package manager contract not properly initialized');

        const packageManagerAddress = contracts.packageManager.target || contracts.packageManager.address;
        console.log('handlePurchase: Purchasing package from PackageManager:', {
          packageManagerAddress,
          packageId: pkg.id,
          referrerAddress,
          userAddress: address,
          packageCostOriginal: pkg.entryUSDT.toString(),
          packageCostForContract: packageCostForContract.toString(),
          packageCostUSDT: packageCostUSDT.toString(),
          contractWillAttemptTransfer: packageCostForContract.toString() + ' (18 decimals)',
          actualUSDTEquivalent: packageCostUSDT.toString() + ' (6 decimals)',
          currentAllowance: allowance.toString(),
          isAllowanceSufficient: allowance >= packageCostForContract
        });

        const tx = await contracts.packageManager.purchase(pkg.id, referrerAddress);

        // Wait for transaction confirmation
        const receipt = await tx.wait();
        return { tx, receipt };
      },
      {
        onStart: () => {
          setLoading(true);
          toast.loading('Processing purchase...', { id: 'purchase' });
        },
        onSuccess: ({ tx }) => {
          const txHash = tx.hash;

          // Show enhanced success message with transaction details
          toast.success(
            createTransactionSuccessToast('Package purchased successfully!', txHash),
            { id: 'purchase', duration: 8000 }
          );

          // Trigger post-transaction actions
          handlePostTransactionSuccess();
        },
        onError: (error) => {
          console.error('Purchase error:', error);

          // Show user-friendly error message
          if (error.isUserRejection) {
            toast.error('Transaction cancelled', { id: 'purchase' });
          } else if (error.isInsufficientFunds) {
            toast.error('Insufficient funds for this transaction', { id: 'purchase' });
          } else if (error.isNetworkError) {
            toast.error('Network error. Please check your connection and try again', { id: 'purchase' });
          } else {
            toast.error(error.userMessage, { id: 'purchase' });
          }
        },
        onFinally: () => {
          setLoading(false);
        }
      }
    );

    return result.success;
  };

  const handlePostTransactionSuccess = () => {
    // Clear form state
    setReferrer('');

    // Reset loading state (should already be done by onFinally, but ensure it's reset)
    setLoading(false);

    // Close the modal after a brief delay to allow user to see success message
    setTimeout(() => {
      onClose();
    }, 1000);

    // Trigger package list refresh and other success actions
    // This will refresh the package data and user balances in the parent component
    onSuccess();
  };

  // Use the converted amount for balance comparison (actual USDT token decimals)
  const hasInsufficientBalance = usdtBalance < packageCostUSDT;

  // Debug logging for balance comparison
  console.log('PurchaseModal: Balance check with decimal conversion:', {
    usdtBalance: usdtBalance.toString(),
    usdtBalanceFormatted: ethers.formatUnits(usdtBalance, usdtDecimals),
    usdtDecimals,
    packageCostOriginal: pkg.entryUSDT.toString(),
    packageCostOriginalFormatted: ethers.formatUnits(pkg.entryUSDT, 18),
    packageCostForContract: packageCostForContract.toString(),
    packageCostForContractFormatted: ethers.formatUnits(packageCostForContract, isLegacyPackage ? 18 : 6),
    packageCostUSDT: packageCostUSDT.toString(),
    packageCostUSDTFormatted: ethers.formatUnits(packageCostUSDT, usdtDecimals),
    isLegacyPackage,
    hasInsufficientBalance,
    address,
    isConnected,
    allowance: allowance.toString(),
    allowanceFormatted: ethers.formatUnits(allowance, usdtDecimals),
    needsApproval,
    contractsAvailable: !!contracts,
    criticalNote: 'Contract will try to transfer packageCostForContract (18 decimals) but USDT has 6 decimals'
  });

  return (
    <Dialog open={isOpen} onClose={onClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/25 backdrop-blur-sm" />
      
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel className="mx-auto max-w-2xl w-full bg-white rounded-2xl shadow-2xl animate-slide-up">
          <div className="p-6">
            <div className="flex items-center justify-between mb-6">
              <Dialog.Title className="text-2xl font-bold text-gray-900">
                Purchase {pkg.name}
              </Dialog.Title>
              <button
                onClick={onClose}
                className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <div className="space-y-6">
              {/* Package Summary */}
              <Card className="border-primary-200 bg-primary-50/50">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="font-semibold text-gray-900">Package Summary</h3>
                    <div className="text-2xl font-bold text-primary-600">
                      ${formatUSDT(pkg.entryUSDT)} USDT
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-600">LP Pool USDT</p>
                      <p className="font-medium">${formatUSDT(splits.usdtPool)}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Treasury USDT</p>
                      <p className="font-medium">${formatUSDT(splits.usdtVault)}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">LP Pool SHARE</p>
                      <p className="font-medium">{formatUSDT(splits.poolTokens)}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Vested SHARE</p>
                      <p className="font-medium">{formatUSDT(splits.vestTokens)}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Referrer Input */}
              <div>
                <Input
                  label="Referrer Address (Optional)"
                  placeholder="0x..."
                  value={referrer}
                  onChange={(e) => setReferrer(e.target.value)}
                  disabled={loading}
                />
                {pkg.referralBps > 0 && (
                  <div className="mt-2 flex items-center text-sm text-accent-600">
                    <Info className="h-4 w-4 mr-1" />
                    Referrer will receive {formatPercentage(pkg.referralBps)} bonus
                  </div>
                )}
              </div>

              {/* Balance Info */}
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Your USDT Balance:</span>
                  <span className={`font-medium ${hasInsufficientBalance ? 'text-error-600' : 'text-gray-900'}`}>
                    ${formatTokenDisplay(usdtBalance)}
                  </span>
                </div>
                {hasInsufficientBalance && (
                  <p className="text-error-600 text-sm mt-2">
                    Insufficient USDT balance for this purchase
                  </p>
                )}

                {/* Debug Info for Decimal Mismatch */}
                {process.env.NODE_ENV === 'development' && (
                  <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs">
                    <p className="font-medium text-yellow-800 mb-1">Debug Info (Dev Mode):</p>
                    <p className="text-yellow-700">Package Type: {isLegacyPackage ? 'Legacy (18 decimals)' : 'New (6 decimals)'}</p>
                    <p className="text-yellow-700">Package Cost (Contract): {ethers.formatUnits(packageCostForContract, isLegacyPackage ? 18 : 6)}</p>
                    <p className="text-yellow-700">Package Cost (USDT): {ethers.formatUnits(packageCostUSDT, usdtDecimals)} (6 decimals)</p>
                    <p className="text-yellow-700">Current Allowance: {ethers.formatUnits(allowance, usdtDecimals)} USDT</p>
                    <p className="text-yellow-700">Needs Approval: {needsApproval ? 'Yes' : 'No'}</p>
                    {isLegacyPackage && (
                      <p className="text-red-700 font-medium">⚠️ Legacy package - may fail due to decimal mismatch!</p>
                    )}
                  </div>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-3">
                {needsApproval ? (
                  <Button
                    onClick={handleApprove}
                    loading={loading}
                    disabled={
                      loading ||
                      !isConnected ||
                      !signer ||
                      hasInsufficientBalance ||
                      !isCorrectNetwork
                    }
                    className="flex-1"
                  >
                    Approve USDT
                  </Button>
                ) : (
                  <Button
                    onClick={handlePurchase}
                    loading={loading}
                    disabled={
                      loading ||
                      !isConnected ||
                      !signer ||
                      hasInsufficientBalance ||
                      !isCorrectNetwork
                    }
                    className="flex-1"
                  >
                    <TrendingUp className="h-4 w-4 mr-2" />
                    Purchase Package
                  </Button>
                )}
                <Button variant="outline" onClick={onClose} className="flex-1">
                  Cancel
                </Button>
              </div>
            </div>
          </div>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
}
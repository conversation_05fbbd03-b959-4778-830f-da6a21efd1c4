import React, { useState, useEffect } from 'react';
import { Dialog } from '@headlessui/react';
import { ethers } from 'ethers';
import { Package, getContracts } from '../../lib/contracts';
import { parseEther } from '../../lib/utils';
import { useWeb3 } from '../../providers/Web3Provider';
import { validateWalletForTransaction, debugWalletState } from '../../lib/walletValidation';
import { useWalletRefresh, shouldAttemptWalletRefresh } from '../../lib/walletRefresh';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { X } from 'lucide-react';
import toast from 'react-hot-toast';

interface PackageFormProps {
  package?: Package | null;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export function PackageForm({ package: pkg, isOpen, onClose, onSuccess }: PackageFormProps) {
  // Use Web3Provider context for proper signer access
  const { provider, signer, isConnected, account, isCorrectNetwork, chainId, connectWallet, switchToCorrectNetwork } = useWeb3();

  // Wallet refresh utility for handling signer timing issues
  const { refreshWallet } = useWalletRefresh();

  const [formData, setFormData] = useState({
    id: '',
    name: '',
    entryUSDT: '',
    exchangeRateBps: '',
    vestBps: '',
    cliff: '',
    duration: '',
    referralBps: '',
  });
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    if (pkg) {
      // CRITICAL FIX: Detect if package uses 18 decimals (legacy) or 6 decimals (new format)
      // Legacy packages have very large values (>1e15), new packages have smaller values
      const isLegacyPackage = pkg.entryUSDT > 1000000000000000n; // > 1e15
      const displayValue = isLegacyPackage
        ? ethers.formatEther(pkg.entryUSDT) // Legacy: 18 decimals
        : ethers.formatUnits(pkg.entryUSDT, 6); // New: 6 decimals

      setFormData({
        id: pkg.id.toString(),
        name: pkg.name,
        entryUSDT: displayValue,
        exchangeRateBps: pkg.exchangeRateBps.toString(),
        vestBps: pkg.vestBps.toString(),
        cliff: pkg.cliff.toString(),
        duration: pkg.duration.toString(),
        referralBps: pkg.referralBps.toString(),
      });
    } else {
      setFormData({
        id: '',
        name: '',
        entryUSDT: '',
        exchangeRateBps: '10000',
        vestBps: '7000',
        cliff: '********',
        duration: '*********',
        referralBps: '200',
      });
    }
  }, [pkg]);

  const handleWalletRefresh = async () => {
    if (!account) {
      toast.error('No account connected');
      return;
    }

    setRefreshing(true);
    try {
      console.log('PackageForm: Manual wallet refresh triggered');
      const refreshResult = await refreshWallet(account, { showToasts: true });

      if (refreshResult.success) {
        toast.success('Wallet connection refreshed successfully');
      } else {
        toast.error(`Failed to refresh wallet: ${refreshResult.error}`);
      }
    } catch (error) {
      console.error('Manual wallet refresh error:', error);
      toast.error('Error refreshing wallet connection');
    } finally {
      setRefreshing(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setLoading(true);

      // Debug wallet state for troubleshooting
      console.log('PackageForm: Starting wallet validation...');
      await debugWalletState(provider, signer, account, chainId);

      // Check if we should attempt wallet refresh for signer timing issues
      if (shouldAttemptWalletRefresh(isConnected, account, signer, null)) {
        console.log('PackageForm: Attempting wallet refresh due to missing signer...');
        toast.loading('Refreshing wallet connection...', { id: 'wallet-refresh' });

        try {
          const refreshResult = await refreshWallet(account!, { showToasts: false });

          if (refreshResult.success && refreshResult.signer) {
            console.log('PackageForm: Wallet refresh successful, using refreshed signer');
            toast.success('Wallet connection refreshed', { id: 'wallet-refresh' });

            // Use the refreshed signer for the transaction
            const contracts = getContracts(refreshResult.signer);

            // CRITICAL FIX: Use USDT decimals (6) instead of 18 decimals for package creation
            // This ensures the smart contract can properly transfer the USDT amount
            const packageData = {
              id: parseInt(formData.id) || Date.now(),
              name: formData.name,
              entryUSDT: ethers.parseUnits(formData.entryUSDT, 6), // Use 6 decimals for USDT
              exchangeRateBps: parseInt(formData.exchangeRateBps),
              vestBps: parseInt(formData.vestBps),
              cliff: parseInt(formData.cliff),
              duration: parseInt(formData.duration),
              referralBps: parseInt(formData.referralBps),
            };

            const tx = await contracts.packageManager.addPackage(
              packageData.id,
              packageData.name,
              packageData.entryUSDT,
              packageData.exchangeRateBps,
              packageData.vestBps,
              packageData.cliff,
              packageData.duration,
              packageData.referralBps
            );

            toast.loading(pkg ? 'Updating package...' : 'Creating package...', { id: 'package' });
            await tx.wait();
            toast.success(pkg ? 'Package updated successfully!' : 'Package created successfully!', { id: 'package' });

            onSuccess();
            return;
          } else {
            console.warn('PackageForm: Wallet refresh failed:', refreshResult.error);
            toast.error('Failed to refresh wallet connection', { id: 'wallet-refresh' });
          }
        } catch (refreshError) {
          console.error('PackageForm: Wallet refresh error:', refreshError);
          toast.error('Error refreshing wallet connection', { id: 'wallet-refresh' });
        }
      }

      // Comprehensive wallet validation using utility function
      const validation = await validateWalletForTransaction(
        signer,
        account,
        chainId
      );

      if (!validation.isValid) {
        // Handle wallet connection issues
        if (!isConnected || !account) {
          console.log('PackageForm: Wallet not connected, opening connection modal...');
          await connectWallet();
          return;
        }

        // Handle network issues
        if (!isCorrectNetwork) {
          console.log('PackageForm: Wrong network, attempting to switch...');
          const switched = await switchToCorrectNetwork();
          if (!switched) {
            throw new Error('Please switch to BSC Testnet to continue');
          }
          return;
        }

        // Handle other validation errors
        const errorMessage = validation.errors.length > 0 ? validation.errors[0] : 'Wallet validation failed';
        throw new Error(errorMessage);
      }

      // Validate form data before proceeding
      if (!formData.name.trim()) {
        throw new Error('Package name is required');
      }
      if (!formData.entryUSDT || parseFloat(formData.entryUSDT) <= 0) {
        throw new Error('Entry USDT amount must be greater than 0');
      }
      if (!formData.exchangeRateBps || parseInt(formData.exchangeRateBps) <= 0) {
        throw new Error('Exchange rate must be greater than 0');
      }
      if (!formData.vestBps || parseInt(formData.vestBps) < 0 || parseInt(formData.vestBps) > 10000) {
        throw new Error('Vest percentage must be between 0 and 10000 BPS');
      }

      const contracts = getContracts(signer);

      // CRITICAL FIX: Use USDT decimals (6) instead of 18 decimals for package creation
      // This ensures the smart contract can properly transfer the USDT amount
      const packageData = {
        id: parseInt(formData.id) || Date.now(),
        name: formData.name.trim(),
        entryUSDT: ethers.parseUnits(formData.entryUSDT, 6), // Use 6 decimals for USDT
        exchangeRateBps: parseInt(formData.exchangeRateBps),
        vestBps: parseInt(formData.vestBps),
        cliff: parseInt(formData.cliff) || 0,
        duration: parseInt(formData.duration) || 0,
        referralBps: parseInt(formData.referralBps) || 0,
      };

      const tx = await contracts.packageManager.addPackage(
        packageData.id,
        packageData.name,
        packageData.entryUSDT,
        packageData.exchangeRateBps,
        packageData.vestBps,
        packageData.cliff,
        packageData.duration,
        packageData.referralBps
      );

      toast.loading(pkg ? 'Updating package...' : 'Creating package...', { id: 'package' });
      await tx.wait();
      toast.success(pkg ? 'Package updated successfully!' : 'Package created successfully!', { id: 'package' });
      
      onSuccess();
    } catch (error: any) {
      console.error('Package form error:', error);
      toast.error(error.message || 'Failed to save package', { id: 'package' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onClose={onClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/25 backdrop-blur-sm" />
      
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel className="mx-auto max-w-2xl w-full bg-white rounded-2xl shadow-2xl animate-slide-up">
          <div className="p-6">
            <div className="flex items-center justify-between mb-6">
              <Dialog.Title className="text-2xl font-bold text-gray-900">
                {pkg ? 'Edit Package' : 'Create Package'}
              </Dialog.Title>
              <button
                onClick={onClose}
                className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Input
                  label="Package ID"
                  type="number"
                  value={formData.id}
                  onChange={(e) => setFormData(prev => ({ ...prev, id: e.target.value }))}
                  required
                />
                
                <Input
                  label="Package Name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="e.g., Starter Package"
                  required
                />

                <Input
                  label="Entry Cost (USDT)"
                  type="number"
                  step="0.01"
                  value={formData.entryUSDT}
                  onChange={(e) => setFormData(prev => ({ ...prev, entryUSDT: e.target.value }))}
                  placeholder="1000"
                  required
                />

                <Input
                  label="Exchange Rate (BPS)"
                  type="number"
                  value={formData.exchangeRateBps}
                  onChange={(e) => setFormData(prev => ({ ...prev, exchangeRateBps: e.target.value }))}
                  placeholder="10000 (1:1 ratio)"
                  required
                />

                <Input
                  label="Vest Percentage (BPS)"
                  type="number"
                  value={formData.vestBps}
                  onChange={(e) => setFormData(prev => ({ ...prev, vestBps: e.target.value }))}
                  placeholder="7000 (70%)"
                  required
                />

                <Input
                  label="Referral Bonus (BPS)"
                  type="number"
                  value={formData.referralBps}
                  onChange={(e) => setFormData(prev => ({ ...prev, referralBps: e.target.value }))}
                  placeholder="200 (2%)"
                  required
                />

                <Input
                  label="Cliff Period (Seconds)"
                  type="number"
                  value={formData.cliff}
                  onChange={(e) => setFormData(prev => ({ ...prev, cliff: e.target.value }))}
                  placeholder="******** (1 year)"
                  required
                />

                <Input
                  label="Vesting Duration (Seconds)"
                  type="number"
                  value={formData.duration}
                  onChange={(e) => setFormData(prev => ({ ...prev, duration: e.target.value }))}
                  placeholder="********* (5 years)"
                  required
                />
              </div>

              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">Preview</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Vest %:</span>
                    <span className="ml-2 font-medium">{(parseInt(formData.vestBps || '0') / 100).toFixed(1)}%</span>
                  </div>
                  <div>
                    <span className="text-gray-600">LP %:</span>
                    <span className="ml-2 font-medium">{((10000 - parseInt(formData.vestBps || '0')) / 100).toFixed(1)}%</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Cliff:</span>
                    <span className="ml-2 font-medium">{Math.round(parseInt(formData.cliff || '0') / (365 * 24 * 3600))}y</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Duration:</span>
                    <span className="ml-2 font-medium">{Math.round(parseInt(formData.duration || '0') / (365 * 24 * 3600))}y</span>
                  </div>
                </div>
              </div>

              {/* Wallet Connection Status */}
              {!isConnected && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                    <span className="text-sm font-medium text-yellow-800">
                      Wallet not connected
                    </span>
                  </div>
                  <p className="text-sm text-yellow-700 mt-1">
                    Please connect your wallet to create or update packages.
                  </p>
                </div>
              )}

              {isConnected && !isCorrectNetwork && (
                <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                    <span className="text-sm font-medium text-orange-800">
                      Wrong network
                    </span>
                  </div>
                  <p className="text-sm text-orange-700 mt-1">
                    Please switch to the correct network (BSC Testnet) to proceed.
                  </p>
                </div>
              )}

              {isConnected && isCorrectNetwork && account && !signer && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                        <span className="text-sm font-medium text-yellow-800">
                          Wallet connected but signer not available
                        </span>
                      </div>
                      <p className="text-sm text-yellow-700 mt-1">
                        This may be a timing issue. Try refreshing the wallet connection.
                      </p>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleWalletRefresh}
                      loading={refreshing}
                      className="ml-4"
                    >
                      Refresh
                    </Button>
                  </div>
                </div>
              )}

              {isConnected && isCorrectNetwork && account && signer && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm font-medium text-green-800">
                      Wallet connected and ready
                    </span>
                  </div>
                  <p className="text-sm text-green-700 mt-1">
                    Connected as: {account.slice(0, 6)}...{account.slice(-4)}
                  </p>
                </div>
              )}

              <div className="flex space-x-3">
                <Button
                  type="submit"
                  loading={loading}
                  disabled={!isConnected || !isCorrectNetwork || !signer}
                  className="flex-1"
                >
                  {pkg ? 'Update Package' : 'Create Package'}
                </Button>
                <Button variant="outline" onClick={onClose} className="flex-1">
                  Cancel
                </Button>
              </div>
            </form>
          </div>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
}
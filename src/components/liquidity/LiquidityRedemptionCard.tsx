import React, { useState, useEffect } from 'react';
import { parseEther, formatUnits } from 'ethers';
import { Card, CardContent, CardHeader } from '../ui/Card';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Label } from '../ui/label';
import { Badge } from '../ui/Badge';
import { Separator } from '../ui/separator';
import { Alert, AlertDescription } from '../ui/alert';
import { 
  TrendingDown, 
  Settings, 
  Shield, 
  AlertTriangle, 
  Info,
  Loader2,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import { useLiquidityRedemption, useSlippageTolerance, useLiquidityFormatting } from '../../hooks/useLiquidityRedemption';
import { useBalances } from '../../hooks/useContracts';
import { useWeb3 } from '../../providers/Web3Provider';

interface LiquidityRedemptionCardProps {
  onRedemptionComplete?: () => void;
}

export function LiquidityRedemptionCard({ onRedemptionComplete }: LiquidityRedemptionCardProps) {
  const { isConnected, isCorrectNetwork } = useWeb3();
  const { balances, loading: balancesLoading } = useBalances();
  const {
    preview,
    tokenPrices,
    loading,
    error,
    getPreview,
    executeLiquidityRemoval,
    clearPreview,
    clearError,
    updateSlippageConfig,
    config,
  } = useLiquidityRedemption();

  const {
    tolerance,
    updateTolerance,
    isHighSlippage,
    isVeryHighSlippage,
  } = useSlippageTolerance();

  const {
    formatTokenAmount,
    formatUSDTAmount,
    formatPercentage,
    formatPriceImpact,
  } = useLiquidityFormatting();

  const [lpAmount, setLpAmount] = useState('');
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [customDeadline, setCustomDeadline] = useState('5');

  // Update slippage config when tolerance changes
  useEffect(() => {
    updateSlippageConfig({
      tolerance,
      deadline: parseInt(customDeadline) * 60, // Convert minutes to seconds
    });
  }, [tolerance, customDeadline, updateSlippageConfig]);

  // Auto-preview when amount changes
  useEffect(() => {
    if (lpAmount && parseFloat(lpAmount) > 0) {
      const amount = parseEther(lpAmount);
      if (amount <= balances.lp) {
        getPreview(amount);
      } else {
        clearPreview();
      }
    } else {
      clearPreview();
    }
  }, [lpAmount, balances.lp, getPreview, clearPreview]);

  const handleRedemption = async () => {
    if (!isConnected || !isCorrectNetwork) {
      toast.error('Please connect your wallet and switch to BSC Testnet');
      return;
    }

    if (!lpAmount || parseFloat(lpAmount) <= 0) {
      toast.error('Please enter a valid LP token amount');
      return;
    }

    const amount = parseEther(lpAmount);
    if (amount > balances.lp) {
      toast.error('Insufficient LP token balance');
      return;
    }

    try {
      toast.loading('Removing liquidity from PancakeSwap...', { id: 'liquidity-removal' });
      
      const tx = await executeLiquidityRemoval(amount);
      
      toast.loading('Waiting for confirmation...', { id: 'liquidity-removal' });
      await tx.wait();
      
      toast.success('Liquidity removed successfully!', { id: 'liquidity-removal' });
      
      setLpAmount('');
      clearPreview();
      onRedemptionComplete?.();
    } catch (error: any) {
      console.error('Liquidity removal error:', error);
      toast.error(error.message || 'Failed to remove liquidity', { id: 'liquidity-removal' });
    }
  };

  const handleMaxClick = () => {
    if (balances.lp > 0n) {
      setLpAmount(formatUnits(balances.lp, 18));
    }
  };

  const priceImpactFormatted = preview ? formatPriceImpact(preview.priceImpact) : null;

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <TrendingDown className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold">Remove Liquidity</h3>
              <p className="text-sm text-gray-600">Redeem LP tokens for USDT + ShareToken</p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowAdvanced(!showAdvanced)}
          >
            <Settings className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <XCircle className="h-4 w-4" />
            <AlertDescription>
              {error}
              <Button variant="ghost" size="sm" onClick={clearError} className="ml-2">
                Dismiss
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {/* LP Token Input */}
        <div className="space-y-2">
          <Label htmlFor="lp-amount">LP Token Amount</Label>
          <div className="relative">
            <Input
              id="lp-amount"
              type="number"
              placeholder="0.0"
              value={lpAmount}
              onChange={(e) => setLpAmount(e.target.value)}
              className="pr-16"
            />
            <Button
              variant="ghost"
              size="sm"
              onClick={handleMaxClick}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 text-xs"
              disabled={balancesLoading}
            >
              MAX
            </Button>
          </div>
          <div className="flex justify-between text-sm text-gray-600">
            <span>Balance: {formatTokenAmount(balances.lp)} LP</span>
            {tokenPrices && (
              <span>1 SHARE = ${tokenPrices.shareTokenPriceInUSDT.toFixed(4)} USDT</span>
            )}
          </div>
        </div>

        {/* Advanced Settings */}
        {showAdvanced && (
          <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-2">
              <Settings className="h-4 w-4" />
              <span className="font-medium">Advanced Settings</span>
            </div>

            {/* Slippage Tolerance */}
            <div className="space-y-2">
              <Label>Slippage Tolerance</Label>
              <div className="flex space-x-2">
                {[0.1, 0.5, 1.0].map((preset) => (
                  <Button
                    key={preset}
                    variant={tolerance === preset ? "default" : "outline"}
                    size="sm"
                    onClick={() => updateTolerance(preset)}
                  >
                    {preset}%
                  </Button>
                ))}
                <Input
                  type="number"
                  placeholder="Custom"
                  value={tolerance}
                  onChange={(e) => updateTolerance(parseFloat(e.target.value) || 0.5)}
                  className="w-20"
                  step="0.1"
                  min="0.1"
                  max="50"
                />
              </div>
              {isHighSlippage && (
                <Alert variant={isVeryHighSlippage ? "destructive" : "default"}>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    {isVeryHighSlippage 
                      ? "Very high slippage tolerance! You may lose significant value."
                      : "High slippage tolerance may result in unfavorable rates."
                    }
                  </AlertDescription>
                </Alert>
              )}
            </div>

            {/* Transaction Deadline */}
            <div className="space-y-2">
              <Label>Transaction Deadline</Label>
              <div className="flex items-center space-x-2">
                <Input
                  type="number"
                  value={customDeadline}
                  onChange={(e) => setCustomDeadline(e.target.value)}
                  className="w-20"
                  min="1"
                  max="60"
                />
                <span className="text-sm text-gray-600">minutes</span>
              </div>
            </div>
          </div>
        )}

        {/* Preview */}
        {preview && (
          <div className="space-y-3 p-4 bg-blue-50 rounded-lg">
            <div className="flex items-center space-x-2">
              <Info className="h-4 w-4 text-blue-600" />
              <span className="font-medium text-blue-900">Expected Returns</span>
            </div>

            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>USDT:</span>
                <span className="font-medium">{formatUSDTAmount(preview.expectedUSDT)}</span>
              </div>
              <div className="flex justify-between">
                <span>ShareToken:</span>
                <span className="font-medium">{formatTokenAmount(preview.expectedShareToken)}</span>
              </div>
              <Separator />
              <div className="flex justify-between">
                <span>Minimum USDT:</span>
                <span className="text-gray-600">{formatUSDTAmount(preview.minimumUSDT)}</span>
              </div>
              <div className="flex justify-between">
                <span>Minimum ShareToken:</span>
                <span className="text-gray-600">{formatTokenAmount(preview.minimumShareToken)}</span>
              </div>
              <div className="flex justify-between">
                <span>Price Impact:</span>
                <span className={priceImpactFormatted?.color}>
                  {priceImpactFormatted?.text}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Slippage Tolerance:</span>
                <span>{formatPercentage(preview.slippageTolerance)}</span>
              </div>
            </div>
          </div>
        )}

        {/* MEV Protection Badge */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Shield className="h-4 w-4 text-green-600" />
            <span className="text-sm text-green-600">MEV Protection Enabled</span>
          </div>
          <Badge variant="secondary">BSC Testnet</Badge>
        </div>

        {/* Action Button */}
        <Button
          onClick={handleRedemption}
          disabled={
            !isConnected || 
            !isCorrectNetwork || 
            !lpAmount || 
            parseFloat(lpAmount) <= 0 || 
            parseEther(lpAmount) > balances.lp ||
            loading ||
            !preview
          }
          className="w-full"
          size="lg"
        >
          {loading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Removing Liquidity...
            </>
          ) : (
            <>
              <TrendingDown className="h-4 w-4 mr-2" />
              Remove Liquidity
            </>
          )}
        </Button>

        {/* Info Text */}
        <p className="text-xs text-gray-500 text-center">
          This will remove your liquidity from the PancakeSwap USDT/ShareToken pool and return the underlying tokens to your wallet.
        </p>
      </CardContent>
    </Card>
  );
}

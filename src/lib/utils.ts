import { ethers } from 'ethers';
import clsx, { ClassValue } from 'clsx';

export function cn(...inputs: ClassValue[]) {
  return clsx(inputs);
}

export function formatEther(value: bigint | string, decimals = 4): string {
  try {
    const formatted = ethers.formatEther(value);
    const num = parseFloat(formatted);
    return num.toLocaleString('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: decimals,
    });
  } catch {
    return '0';
  }
}

export function formatUSDT(value: bigint | string): string {
  try {
    const bigintValue = typeof value === 'string' ? BigInt(value) : value;

    // CRITICAL FIX: Detect if this is a legacy value (18 decimals) or new value (6 decimals)
    // Legacy values are very large (>1e15), new values are smaller
    const isLegacyValue = bigintValue > 1000000000000000n; // > 1e15

    const formatted = isLegacyValue
      ? ethers.formatUnits(bigintValue, 18) // Legacy: 18 decimals
      : ethers.formatUnits(bigintValue, 6);  // New: 6 decimals

    const num = parseFloat(formatted);
    return num.toLocaleString('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    });
  } catch {
    return '0';
  }
}

export function formatAddress(address: string): string {
  if (!address) return '';
  return `${address.slice(0, 6)}...${address.slice(-4)}`;
}

export function formatDuration(seconds: number): string {
  const years = Math.floor(seconds / (365 * 24 * 3600));
  const months = Math.floor((seconds % (365 * 24 * 3600)) / (30 * 24 * 3600));
  
  if (years > 0) {
    return months > 0 ? `${years}y ${months}m` : `${years}y`;
  }
  return months > 0 ? `${months}m` : '< 1m';
}

export function formatPercentage(bps: number): string {
  return `${(bps / 100).toFixed(1)}%`;
}

export function parseEther(value: string): bigint {
  try {
    return ethers.parseEther(value || '0');
  } catch {
    return 0n;
  }
}

export function shortenAddress(address: string): string {
  return `${address.slice(0, 6)}...${address.slice(-4)}`;
}
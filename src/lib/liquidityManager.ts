import { ethers, Contract, Signer } from 'ethers';
import { appKitConfig } from './appkit';
import { getContracts } from './contracts';

// Import ABI files
import pancakeRouterAbi from '../abi/IPancakeRouter.json';
import pancakeFactoryAbi from '../abi/IPancakeFactory.json';
import pancakePairAbi from '../abi/IPancakePair.json';
import erc20Abi from '../abi/ERC20.json';

// Types for liquidity management
export interface LiquidityRemovalPreview {
  lpTokenAmount: bigint;
  expectedUSDT: bigint;
  expectedShareToken: bigint;
  minimumUSDT: bigint;
  minimumShareToken: bigint;
  priceImpact: number;
  slippageTolerance: number;
}

export interface SlippageConfig {
  tolerance: number; // Percentage (e.g., 0.5 for 0.5%)
  deadline: number; // Seconds from now
}

export interface MEVProtection {
  usePrivateMempool: boolean;
  maxGasPrice: bigint;
  deadline: number;
}

// Default configurations
export const DEFAULT_SLIPPAGE_CONFIG: SlippageConfig = {
  tolerance: 0.5, // 0.5%
  deadline: 300, // 5 minutes
};

export const DEFAULT_MEV_PROTECTION: MEVProtection = {
  usePrivateMempool: false,
  maxGasPrice: ethers.parseUnits('20', 'gwei'), // 20 Gwei max
  deadline: 300, // 5 minutes
};

/**
 * Enhanced Liquidity Manager for PancakeSwap V2 Integration
 * Handles actual liquidity removal with slippage protection and MEV resistance
 */
export class LiquidityManager {
  private router: Contract;
  private factory: Contract;
  private signer: Signer;

  constructor(signer: Signer) {
    this.signer = signer;
    this.router = new Contract(
      appKitConfig.contracts.router,
      pancakeRouterAbi.abi,
      signer
    );
    
    // Get factory address from router
    this.factory = new Contract(
      '******************************************', // BSC Testnet PancakeSwap Factory
      pancakeFactoryAbi.abi,
      signer
    );
  }

  /**
   * Get the PancakeSwap pair address for ShareToken/USDT
   */
  async getPairAddress(): Promise<string> {
    const shareTokenAddress = appKitConfig.contracts.share;
    const usdtAddress = appKitConfig.contracts.usdt;
    
    const pairAddress = await this.factory.getPair(shareTokenAddress, usdtAddress);
    if (pairAddress === ethers.ZeroAddress) {
      throw new Error('No liquidity pair found for ShareToken/USDT');
    }
    
    return pairAddress;
  }

  /**
   * Get current reserves for the ShareToken/USDT pair
   */
  async getReserves(): Promise<{ reserveShare: bigint; reserveUSDT: bigint; token0: string; token1: string }> {
    const pairAddress = await this.getPairAddress();
    const pair = new Contract(pairAddress, pancakePairAbi.abi, this.signer);
    
    const [reserve0, reserve1] = await pair.getReserves();
    const token0 = await pair.token0();
    const token1 = await pair.token1();
    
    const shareTokenAddress = appKitConfig.contracts.share;
    const usdtAddress = appKitConfig.contracts.usdt;
    
    // Determine which reserve corresponds to which token
    let reserveShare: bigint, reserveUSDT: bigint;
    if (token0.toLowerCase() === shareTokenAddress.toLowerCase()) {
      reserveShare = reserve0;
      reserveUSDT = reserve1;
    } else {
      reserveShare = reserve1;
      reserveUSDT = reserve0;
    }
    
    return { reserveShare, reserveUSDT, token0, token1 };
  }

  /**
   * Calculate expected returns from LP token redemption
   */
  async calculateLiquidityRemoval(
    lpTokenAmount: bigint,
    slippageConfig: SlippageConfig = DEFAULT_SLIPPAGE_CONFIG
  ): Promise<LiquidityRemovalPreview> {
    const pairAddress = await this.getPairAddress();
    const pair = new Contract(pairAddress, pancakePairAbi.abi, this.signer);
    
    // Get total LP supply and reserves
    const [totalSupply, reserves] = await Promise.all([
      pair.totalSupply(),
      this.getReserves()
    ]);
    
    // Calculate proportional amounts
    const shareOfPool = (lpTokenAmount * 10000n) / totalSupply; // Basis points
    const expectedShareToken = (reserves.reserveShare * shareOfPool) / 10000n;
    const expectedUSDT = (reserves.reserveUSDT * shareOfPool) / 10000n;
    
    // Apply slippage tolerance
    const slippageBps = BigInt(Math.floor(slippageConfig.tolerance * 100)); // Convert to basis points
    const minimumShareToken = expectedShareToken - (expectedShareToken * slippageBps) / 10000n;
    const minimumUSDT = expectedUSDT - (expectedUSDT * slippageBps) / 10000n;
    
    // Calculate price impact (simplified)
    const priceImpact = Number(shareOfPool) / 100; // Convert basis points to percentage
    
    return {
      lpTokenAmount,
      expectedUSDT,
      expectedShareToken,
      minimumUSDT,
      minimumShareToken,
      priceImpact,
      slippageTolerance: slippageConfig.tolerance,
    };
  }

  /**
   * Execute liquidity removal with slippage protection
   */
  async removeLiquidity(
    lpTokenAmount: bigint,
    slippageConfig: SlippageConfig = DEFAULT_SLIPPAGE_CONFIG,
    mevProtection: MEVProtection = DEFAULT_MEV_PROTECTION
  ): Promise<ethers.ContractTransactionResponse> {
    // Get removal preview
    const preview = await this.calculateLiquidityRemoval(lpTokenAmount, slippageConfig);
    
    // Check current gas price for MEV protection
    const currentGasPrice = await this.signer.provider!.getGasPrice();
    if (currentGasPrice > mevProtection.maxGasPrice) {
      throw new Error(`Gas price too high: ${ethers.formatUnits(currentGasPrice, 'gwei')} Gwei > ${ethers.formatUnits(mevProtection.maxGasPrice, 'gwei')} Gwei`);
    }
    
    // Calculate deadline
    const deadline = Math.floor(Date.now() / 1000) + mevProtection.deadline;
    
    // Get pair address to approve LP tokens
    const pairAddress = await this.getPairAddress();
    const lpTokenContract = new Contract(pairAddress, erc20Abi.abi, this.signer);
    
    // Approve router to spend LP tokens
    const approveTx = await lpTokenContract.approve(this.router.target, lpTokenAmount);
    await approveTx.wait();
    
    // Execute liquidity removal
    const tx = await this.router.removeLiquidity(
      appKitConfig.contracts.share,
      appKitConfig.contracts.usdt,
      lpTokenAmount,
      preview.minimumShareToken,
      preview.minimumUSDT,
      await this.signer.getAddress(),
      deadline,
      {
        gasPrice: currentGasPrice,
        gasLimit: 300000n, // Conservative gas limit
      }
    );
    
    return tx;
  }

  /**
   * Get current token prices from the pool
   */
  async getTokenPrices(): Promise<{ shareTokenPriceInUSDT: number; usdtPriceInShareToken: number }> {
    const reserves = await this.getReserves();
    
    // Calculate prices based on reserves
    // Price = reserve_other / reserve_this
    const shareTokenPriceInUSDT = Number(reserves.reserveUSDT) / Number(reserves.reserveShare);
    const usdtPriceInShareToken = Number(reserves.reserveShare) / Number(reserves.reserveUSDT);
    
    return {
      shareTokenPriceInUSDT,
      usdtPriceInShareToken,
    };
  }

  /**
   * Estimate gas for liquidity removal
   */
  async estimateRemovalGas(
    lpTokenAmount: bigint,
    slippageConfig: SlippageConfig = DEFAULT_SLIPPAGE_CONFIG
  ): Promise<bigint> {
    const preview = await this.calculateLiquidityRemoval(lpTokenAmount, slippageConfig);
    const deadline = Math.floor(Date.now() / 1000) + slippageConfig.deadline;
    
    try {
      const gasEstimate = await this.router.removeLiquidity.estimateGas(
        appKitConfig.contracts.share,
        appKitConfig.contracts.usdt,
        lpTokenAmount,
        preview.minimumShareToken,
        preview.minimumUSDT,
        await this.signer.getAddress(),
        deadline
      );
      
      // Add 20% buffer
      return gasEstimate + (gasEstimate * 20n) / 100n;
    } catch (error) {
      console.error('Gas estimation failed:', error);
      return 300000n; // Fallback gas limit
    }
  }
}

/**
 * Factory function to create LiquidityManager instance
 */
export function createLiquidityManager(signer: Signer): LiquidityManager {
  return new LiquidityManager(signer);
}

/**
 * Utility function to format slippage percentage
 */
export function formatSlippage(slippage: number): string {
  return `${slippage.toFixed(2)}%`;
}

/**
 * Utility function to calculate price impact color for UI
 */
export function getPriceImpactColor(priceImpact: number): string {
  if (priceImpact < 0.1) return 'text-green-600';
  if (priceImpact < 1) return 'text-yellow-600';
  if (priceImpact < 3) return 'text-orange-600';
  return 'text-red-600';
}

{"_format": "hh-sol-artifact-1", "contractName": "IPancakePair", "sourceName": "interfaces/IPancakePair.sol", "abi": [{"inputs": [], "name": "getReserves", "outputs": [{"internalType": "uint112", "name": "reserve0", "type": "uint112"}, {"internalType": "uint112", "name": "reserve1", "type": "uint112"}, {"internalType": "uint32", "name": "blockTimestampLast", "type": "uint32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "token0", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "token1", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}
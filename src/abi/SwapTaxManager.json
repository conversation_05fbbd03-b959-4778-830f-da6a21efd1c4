{"_format": "hh-sol-artifact-1", "contractName": "SwapTaxManager", "sourceName": "contracts/src/BlockCoopV2.sol", "abi": [{"inputs": [{"internalType": "address", "name": "admin", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "key", "type": "bytes32"}, {"indexed": false, "internalType": "uint16", "name": "rateBps", "type": "uint16"}, {"indexed": false, "internalType": "address", "name": "recipient", "type": "address"}], "name": "BucketSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MANAGER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "name": "buckets", "outputs": [{"internalType": "uint16", "name": "rateBps", "type": "uint16"}, {"internalType": "address", "name": "recipient", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "key", "type": "bytes32"}, {"internalType": "uint16", "name": "rateBps", "type": "uint16"}, {"internalType": "address", "name": "recipient", "type": "address"}], "name": "setBucket", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}
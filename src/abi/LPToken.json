{"_format": "hh-sol-artifact-1", "contractName": "LPToken", "sourceName": "contracts/src/BlockCoopV2.sol", "abi": [{"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "address", "name": "admin", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [], "name": "BURNER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MINTER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "burn", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "subtractedValue", "type": "uint256"}], "name": "decreaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "addedValue", "type": "uint256"}], "name": "increaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "mint", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}
[{"inputs": [{"internalType": "address", "name": "usdt_", "type": "address"}, {"internalType": "address", "name": "share_", "type": "address"}, {"internalType": "address", "name": "lp_", "type": "address"}, {"internalType": "address", "name": "vault_", "type": "address"}, {"internalType": "address", "name": "router_", "type": "address"}, {"internalType": "address", "name": "treasury_", "type": "address"}, {"internalType": "address", "name": "tax_", "type": "address"}, {"internalType": "address", "name": "admin", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "SafeERC20FailedOperation", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "oldWindow", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newWindow", "type": "uint256"}], "name": "DeadlineWindowUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "name", "type": "string"}, {"indexed": false, "internalType": "uint256", "name": "entryUSDT", "type": "uint256"}, {"indexed": false, "internalType": "uint16", "name": "exchangeRateBps", "type": "uint16"}, {"indexed": false, "internalType": "uint16", "name": "vestBps", "type": "uint16"}, {"indexed": false, "internalType": "uint64", "name": "cliff", "type": "uint64"}, {"indexed": false, "internalType": "uint64", "name": "duration", "type": "uint64"}, {"indexed": false, "internalType": "uint16", "name": "referralBps", "type": "uint16"}], "name": "PackageAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": false, "internalType": "bool", "name": "active", "type": "bool"}], "name": "PackageToggled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "buyer", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "packageId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "usdtAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "totalTokens", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "vestTokens", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "poolTokens", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "lpTokens", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "referrer", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "referralReward", "type": "uint256"}], "name": "Purchased", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "lpAmount", "type": "uint256"}], "name": "Redeemed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "taxKey", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "recipient", "type": "address"}], "name": "TaxApplied", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "oldTreasury", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newTreasury", "type": "address"}], "name": "TreasuryUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"inputs": [], "name": "ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PURCHASE_TAX_KEY", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "REFERRAL_TAX_KEY", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "uint256", "name": "entryUSDT", "type": "uint256"}, {"internalType": "uint16", "name": "exchangeRateBps", "type": "uint16"}, {"internalType": "uint16", "name": "vestBps", "type": "uint16"}, {"internalType": "uint64", "name": "cliff", "type": "uint64"}, {"internalType": "uint64", "name": "duration", "type": "uint64"}, {"internalType": "uint16", "name": "referralBps", "type": "uint16"}], "name": "addPackage", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "deadlineWindow", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "emergencyRecoverToken", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getActivePackageIds", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}], "name": "getPackage", "outputs": [{"components": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "uint256", "name": "entryUSDT", "type": "uint256"}, {"internalType": "uint16", "name": "exchangeRateBps", "type": "uint16"}, {"internalType": "uint16", "name": "vestBps", "type": "uint16"}, {"internalType": "uint64", "name": "cliff", "type": "uint64"}, {"internalType": "uint64", "name": "duration", "type": "uint64"}, {"internalType": "uint16", "name": "referralBps", "type": "uint16"}, {"internalType": "bool", "name": "active", "type": "bool"}, {"internalType": "bool", "name": "exists", "type": "bool"}], "internalType": "struct PackageManagerV2_1.Package", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getPackageCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getPackageIds", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "getPackagesByOwner", "outputs": [{"components": [{"internalType": "uint256", "name": "packageId", "type": "uint256"}, {"internalType": "uint256", "name": "usdtAmount", "type": "uint256"}, {"internalType": "uint256", "name": "totalTokens", "type": "uint256"}, {"internalType": "uint256", "name": "vestTokens", "type": "uint256"}, {"internalType": "uint256", "name": "poolTokens", "type": "uint256"}, {"internalType": "uint256", "name": "lpTokens", "type": "uint256"}, {"internalType": "address", "name": "referrer", "type": "address"}, {"internalType": "uint256", "name": "referralReward", "type": "uint256"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}], "internalType": "struct PackageManagerV2_1.UserPurchase[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getUserPackageCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getUserPackages", "outputs": [{"components": [{"internalType": "uint256", "name": "packageId", "type": "uint256"}, {"internalType": "uint256", "name": "usdtAmount", "type": "uint256"}, {"internalType": "uint256", "name": "totalTokens", "type": "uint256"}, {"internalType": "uint256", "name": "vestTokens", "type": "uint256"}, {"internalType": "uint256", "name": "poolTokens", "type": "uint256"}, {"internalType": "uint256", "name": "lpTokens", "type": "uint256"}, {"internalType": "address", "name": "referrer", "type": "address"}, {"internalType": "uint256", "name": "referralReward", "type": "uint256"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}], "internalType": "struct PackageManagerV2_1.UserPurchase[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "index", "type": "uint256"}], "name": "getUserPurchase", "outputs": [{"components": [{"internalType": "uint256", "name": "packageId", "type": "uint256"}, {"internalType": "uint256", "name": "usdtAmount", "type": "uint256"}, {"internalType": "uint256", "name": "totalTokens", "type": "uint256"}, {"internalType": "uint256", "name": "vestTokens", "type": "uint256"}, {"internalType": "uint256", "name": "poolTokens", "type": "uint256"}, {"internalType": "uint256", "name": "lpTokens", "type": "uint256"}, {"internalType": "address", "name": "referrer", "type": "address"}, {"internalType": "uint256", "name": "referralReward", "type": "uint256"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}], "internalType": "struct PackageManagerV2_1.UserPurchase", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getUserPurchaseCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getUserPurchases", "outputs": [{"components": [{"internalType": "uint256", "name": "packageId", "type": "uint256"}, {"internalType": "uint256", "name": "usdtAmount", "type": "uint256"}, {"internalType": "uint256", "name": "totalTokens", "type": "uint256"}, {"internalType": "uint256", "name": "vestTokens", "type": "uint256"}, {"internalType": "uint256", "name": "poolTokens", "type": "uint256"}, {"internalType": "uint256", "name": "lpTokens", "type": "uint256"}, {"internalType": "address", "name": "referrer", "type": "address"}, {"internalType": "uint256", "name": "referralReward", "type": "uint256"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}], "internalType": "struct PackageManagerV2_1.UserPurchase[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getUserRedemptionCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getUserRedemptions", "outputs": [{"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "timestamps", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getUserStats", "outputs": [{"components": [{"internalType": "uint256", "name": "totalInvested", "type": "uint256"}, {"internalType": "uint256", "name": "totalTokensReceived", "type": "uint256"}, {"internalType": "uint256", "name": "totalVestTokens", "type": "uint256"}, {"internalType": "uint256", "name": "totalPoolTokens", "type": "uint256"}, {"internalType": "uint256", "name": "totalLPTokens", "type": "uint256"}, {"internalType": "uint256", "name": "totalReferralRewards", "type": "uint256"}, {"internalType": "uint256", "name": "purchaseCount", "type": "uint256"}, {"internalType": "uint256", "name": "redemptionCount", "type": "uint256"}, {"internalType": "uint256", "name": "totalRedemptions", "type": "uint256"}], "internalType": "struct PackageManagerV2_1.UserStats", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "lpToken", "outputs": [{"internalType": "contract ILPToken", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "nextPackageId", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "address", "name": "referrer", "type": "address"}], "name": "purchase", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "lpAmount", "type": "uint256"}], "name": "redeem", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "router", "outputs": [{"internalType": "contract IPancakeRouter", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newWindow", "type": "uint256"}], "name": "setDeadlineWindow", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newTreasury", "type": "address"}], "name": "setTreasury", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "shareToken", "outputs": [{"internalType": "contract IShareToken", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "taxManager", "outputs": [{"internalType": "contract ISwapTaxManager", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}], "name": "togglePackage", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "treasury", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "usdt", "outputs": [{"internalType": "contract IERC20Decimals", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "vesting<PERSON><PERSON>", "outputs": [{"internalType": "contract IVestingVault", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}]
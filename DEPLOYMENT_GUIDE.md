# PackageManagerV2_1 Enhanced Contract Deployment Guide

## Overview

This guide covers deploying the enhanced PackageManagerV2_1 contract with new view functions for efficient portfolio data retrieval.

## Prerequisites

- Solidity compiler version 0.8.19 or higher
- Access to BSC Testnet/Mainnet
- Deployment wallet with sufficient BNB for gas fees
- All dependency contracts deployed (USDT, ShareToken, LPToken, VestingVault, etc.)

## Contract Changes Summary

### New Storage Variables
```solidity
// User portfolio tracking
mapping(address => UserStats) private _userStats;
mapping(address => UserPurchase[]) private _userPurchases;
mapping(address => uint256[]) private _userRedemptions;
mapping(address => uint256[]) private _userRedemptionTimestamps;
```

### New View Functions
- `getUserStats(address user)` - Get aggregated user statistics
- `getUserPurchases(address user)` - Get user's purchase history
- `getUserRedemptions(address user)` - Get user's redemption history
- `getUserPurchaseCount(address user)` - Get user's purchase count
- `getUserRedemptionCount(address user)` - Get user's redemption count

### Enhanced Functions
- `purchase()` - Now updates user data storage
- `redeem()` - Now updates user redemption data

## Deployment Steps

### 1. Compile Contract
```bash
# Using Hardhat
npx hardhat compile

# Using Foundry
forge build
```

### 2. Deploy Contract
```javascript
// Example deployment script
const PackageManagerV2_1 = await ethers.getContractFactory("PackageManagerV2_1");
const packageManager = await PackageManagerV2_1.deploy(
  usdtAddress,      // USDT token address
  shareAddress,     // Share token address
  lpAddress,        // LP token address
  vaultAddress,     // Vesting vault address
  routerAddress,    // PancakeSwap router address
  treasuryAddress,  // Treasury address
  taxManagerAddress, // Tax manager address
  adminAddress      // Admin address
);

await packageManager.deployed();
console.log("PackageManagerV2_1 deployed to:", packageManager.address);
```

### 3. Verify Contract
```bash
# Using Hardhat
npx hardhat verify --network bsc-testnet <CONTRACT_ADDRESS> <CONSTRUCTOR_ARGS>

# Using Foundry
forge verify-contract <CONTRACT_ADDRESS> PackageManagerV2_1 --chain-id 97
```

### 4. Grant Required Roles
```javascript
// Grant LOCKER_ROLE to PackageManager on VestingVault
await vestingVault.grantRole(LOCKER_ROLE, packageManager.address);

// Verify role was granted
const hasRole = await vestingVault.hasRole(LOCKER_ROLE, packageManager.address);
console.log("PackageManager has LOCKER_ROLE:", hasRole);
```

### 5. Update Frontend Configuration
```javascript
// Update contract address in appkit.ts or config file
export const appKitConfig = {
  contracts: {
    packageManager: "NEW_CONTRACT_ADDRESS",
    // ... other contracts
  }
};
```

## Migration Considerations

### Data Migration
- **New deployments**: No migration needed, data will populate as users interact
- **Existing deployments**: Consider migration script to populate historical data

### Frontend Compatibility
- New frontend is backward compatible
- Will automatically use new efficient functions when available
- Falls back to event-based queries if needed

### Testing Checklist

- [ ] Contract deploys successfully
- [ ] All view functions return expected data types
- [ ] Purchase function updates user data correctly
- [ ] Redemption function updates user data correctly
- [ ] Frontend loads portfolio data instantly
- [ ] Fallback to event-based data works if needed
- [ ] No RPC rate limiting occurs during normal usage

## Rollback Plan

If issues occur:
1. Frontend automatically falls back to event-based queries
2. Can revert to previous contract address in configuration
3. No data loss as events are still emitted

## Performance Monitoring

Monitor these metrics post-deployment:
- Portfolio page load times
- RPC call frequency
- Error rates
- User experience feedback

## Support

For deployment issues or questions:
- Check contract compilation errors
- Verify all constructor parameters
- Ensure proper role assignments
- Test on testnet before mainnet deployment

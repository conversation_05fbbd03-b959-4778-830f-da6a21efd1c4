{"name": "blockcoop-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^1.7.17", "@openzeppelin/contracts": "^5.3.0", "@reown/appkit": "^1.7.11", "@reown/appkit-adapter-ethers": "^1.7.11", "@tanstack/react-query": "^5.81.5", "buffer": "^6.0.3", "clsx": "^2.0.0", "dotenv": "^17.0.0", "ethers": "^6.15.0", "lru-cache": "^11.1.0", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.4.1", "react-router-dom": "^6.26.0"}, "devDependencies": {"@eslint/js": "^9.9.1", "@nomicfoundation/hardhat-toolbox": "^6.0.0", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "hardhat": "^2.25.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}
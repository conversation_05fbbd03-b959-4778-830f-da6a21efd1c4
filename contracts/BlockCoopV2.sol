// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";

/**
 * @title ShareToken
 * @dev ERC20 token representing BlockCoop shares. Mintable by PackageManager.
 */
contract ShareToken is ERC20, AccessControl {
    bytes32 public constant MINTER_ROLE = keccak256("MINTER_ROLE");

    constructor(string memory name, string memory symbol, address admin) ERC20(name, symbol) {
        _grantRole(DEFAULT_ADMIN_ROLE, admin);
    }

    function mint(address to, uint256 amount) external onlyRole(MINTER_ROLE) {
        _mint(to, amount);
    }
}

/**
 * @title LPToken
 * @dev ERC20 token representing liquidity provider shares. Minted on purchase and burnable.
 */
contract LPToken is ERC20, AccessControl {
    bytes32 public constant MINTER_ROLE = keccak256("MINTER_ROLE");
    bytes32 public constant BURNER_ROLE = keccak256("BURNER_ROLE");

    constructor(string memory name, string memory symbol, address admin) ERC20(name, symbol) {
        _grantRole(DEFAULT_ADMIN_ROLE, admin);
    }

    function mint(address to, uint256 amount) external onlyRole(MINTER_ROLE) {
        _mint(to, amount);
    }

    function burn(address from, uint256 amount) external onlyRole(BURNER_ROLE) {
        _burn(from, amount);
    }
}

/**
 * @title VestingVault
 * @dev Per-user linear vesting with cliff, uses Schedule per user.
 */
contract VestingVault is AccessControl, ReentrancyGuard {
    using SafeERC20 for IERC20;

    bytes32 public constant LOCKER_ROLE = keccak256("LOCKER_ROLE");

    struct Schedule {
        uint64 cliff;
        uint64 duration;
        uint256 start;
    }

    ShareToken public immutable shareToken;
    mapping(address => uint256) public totalLocked;
    mapping(address => uint256) public released;
    mapping(address => Schedule) public userSchedule;

    event Locked(address indexed user, uint256 amount, uint64 cliff, uint64 duration);
    event Claimed(address indexed user, uint256 amount);

    constructor(address shareToken_, address admin) {
        shareToken = ShareToken(shareToken_);
        _grantRole(DEFAULT_ADMIN_ROLE, admin);
    }

    function lock(
        address user,
        uint256 amount,
        uint64 cliff,
        uint64 duration
    ) external onlyRole(LOCKER_ROLE) {
        // set or override per-user schedule
        userSchedule[user] = Schedule(cliff, duration, block.timestamp);
        totalLocked[user] += amount;
        shareToken.mint(address(this), amount);
        emit Locked(user, amount, cliff, duration);
    }

    function claim() external nonReentrant {
        uint256 vested = vestedAmount(msg.sender);
        uint256 unreleased = vested - released[msg.sender];
        require(unreleased > 0, "Nothing to claim");

        released[msg.sender] += unreleased;
        IERC20(address(shareToken)).safeTransfer(msg.sender, unreleased);
        emit Claimed(msg.sender, unreleased);
    }

    function vestedAmount(address user) public view returns (uint256) {
        Schedule memory s = userSchedule[user];
        uint256 startCliff = s.start + s.cliff;
        if (block.timestamp < startCliff) return 0;
        uint256 total = totalLocked[user];
        uint256 elapsed = block.timestamp - startCliff;
        if (elapsed >= s.duration) return total;
        return (total * elapsed) / s.duration;
    }
}

/**
 * @title SwapTaxManager
 * @dev Configurable tax buckets and recipients for AMM trades.
 */
contract SwapTaxManager is AccessControl {
    bytes32 public constant MANAGER_ROLE = keccak256("MANAGER_ROLE");

    struct TaxBucket { uint16 rateBps; address recipient; }
    mapping(bytes32 => TaxBucket) public buckets;

    event BucketSet(bytes32 indexed key, uint16 rateBps, address recipient);

    constructor(address admin) {
        _grantRole(DEFAULT_ADMIN_ROLE, admin);
    }

    function setBucket(bytes32 key, uint16 rateBps, address recipient) external onlyRole(MANAGER_ROLE) {
        require(rateBps <= 10000, "Rate too high");
        buckets[key] = TaxBucket(rateBps, recipient);
        emit BucketSet(key, rateBps, recipient);
    }

    function getTaxBucket(bytes32 key) external view returns (TaxBucket memory) {
        return buckets[key];
    }
}

/**
 * @dev Minimal PancakeRouter interface for addLiquidity
 */
interface IPancakeRouter {
    function addLiquidity(
        address tokenA,
        address tokenB,
        uint amountADesired,
        uint amountBDesired,
        uint amountAMin,
        uint amountBMin,
        address to,
        uint deadline
    ) external returns (uint amountA, uint amountB, uint liquidity);
}

/**
 * @notice The old PackageManager contract has been removed from this file.
 * @dev Use the standalone PackageManagerV2_1.sol contract instead, which provides:
 *      - Enhanced view functions for portfolio data (getUserPackages, getUserPackageCount, getPackagesByOwner)
 *      - Better user tracking and statistics
 *      - Improved gas efficiency with optimized storage
 *      - Backward compatibility with existing functionality
 *      - Event-based fallback for frontend integration
 *
 * This file now contains only the essential supporting contracts:
 * - ShareToken: ERC20 token for BlockCoop shares
 * - LPToken: ERC20 token for liquidity provider shares
 * - VestingVault: Linear vesting with cliff functionality
 * - SwapTaxManager: Configurable tax buckets for AMM trades
 * - IPancakeRouter: Interface for PancakeSwap integration
 */


{"_format": "hh-sol-artifact-1", "contractName": "SafeERC20", "sourceName": "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol", "abi": [{"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "currentAllowance", "type": "uint256"}, {"internalType": "uint256", "name": "requestedDecrease", "type": "uint256"}], "name": "SafeERC20FailedDecreaseAllowance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "SafeERC20FailedOperation", "type": "error"}], "bytecode": "0x60808060405234601757603a9081601d823930815050f35b600080fdfe600080fdfea2646970667358221220120167e250e19653c18348e566d8624234704be91b7a954312f9736c7bf962df64736f6c63430008140033", "deployedBytecode": "0x600080fdfea2646970667358221220120167e250e19653c18348e566d8624234704be91b7a954312f9736c7bf962df64736f6c63430008140033", "linkReferences": {}, "deployedLinkReferences": {}}
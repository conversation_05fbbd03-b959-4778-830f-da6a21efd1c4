{"_format": "hh-sol-artifact-1", "contractName": "PackageManagerV2_1", "sourceName": "contracts/PackageManagerV2_1.sol", "abi": [{"inputs": [{"internalType": "address", "name": "usdt_", "type": "address"}, {"internalType": "address", "name": "share_", "type": "address"}, {"internalType": "address", "name": "lp_", "type": "address"}, {"internalType": "address", "name": "vault_", "type": "address"}, {"internalType": "address", "name": "router_", "type": "address"}, {"internalType": "address", "name": "treasury_", "type": "address"}, {"internalType": "address", "name": "tax_", "type": "address"}, {"internalType": "address", "name": "admin", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "SafeERC20FailedOperation", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "oldWindow", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newWindow", "type": "uint256"}], "name": "DeadlineWindowUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "name", "type": "string"}, {"indexed": false, "internalType": "uint256", "name": "entryUSDT", "type": "uint256"}, {"indexed": false, "internalType": "uint16", "name": "exchangeRateBps", "type": "uint16"}, {"indexed": false, "internalType": "uint16", "name": "vestBps", "type": "uint16"}, {"indexed": false, "internalType": "uint64", "name": "cliff", "type": "uint64"}, {"indexed": false, "internalType": "uint64", "name": "duration", "type": "uint64"}, {"indexed": false, "internalType": "uint16", "name": "referralBps", "type": "uint16"}], "name": "PackageAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": false, "internalType": "bool", "name": "active", "type": "bool"}], "name": "PackageToggled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "buyer", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "packageId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "usdtAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "totalTokens", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "vestTokens", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "poolTokens", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "lpTokens", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "referrer", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "referralReward", "type": "uint256"}], "name": "Purchased", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "lpAmount", "type": "uint256"}], "name": "Redeemed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "taxKey", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "recipient", "type": "address"}], "name": "TaxApplied", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "oldTreasury", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newTreasury", "type": "address"}], "name": "TreasuryUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"inputs": [], "name": "ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PURCHASE_TAX_KEY", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "REFERRAL_TAX_KEY", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "uint256", "name": "entryUSDT", "type": "uint256"}, {"internalType": "uint16", "name": "exchangeRateBps", "type": "uint16"}, {"internalType": "uint16", "name": "vestBps", "type": "uint16"}, {"internalType": "uint64", "name": "cliff", "type": "uint64"}, {"internalType": "uint64", "name": "duration", "type": "uint64"}, {"internalType": "uint16", "name": "referralBps", "type": "uint16"}], "name": "addPackage", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "deadlineWindow", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "emergencyRecoverToken", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getActivePackageIds", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}], "name": "getPackage", "outputs": [{"components": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "uint256", "name": "entryUSDT", "type": "uint256"}, {"internalType": "uint16", "name": "exchangeRateBps", "type": "uint16"}, {"internalType": "uint16", "name": "vestBps", "type": "uint16"}, {"internalType": "uint64", "name": "cliff", "type": "uint64"}, {"internalType": "uint64", "name": "duration", "type": "uint64"}, {"internalType": "uint16", "name": "referralBps", "type": "uint16"}, {"internalType": "bool", "name": "active", "type": "bool"}, {"internalType": "bool", "name": "exists", "type": "bool"}], "internalType": "struct PackageManagerV2_1.Package", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getPackageCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getPackageIds", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "getPackagesByOwner", "outputs": [{"components": [{"internalType": "uint256", "name": "packageId", "type": "uint256"}, {"internalType": "uint256", "name": "usdtAmount", "type": "uint256"}, {"internalType": "uint256", "name": "totalTokens", "type": "uint256"}, {"internalType": "uint256", "name": "vestTokens", "type": "uint256"}, {"internalType": "uint256", "name": "poolTokens", "type": "uint256"}, {"internalType": "uint256", "name": "lpTokens", "type": "uint256"}, {"internalType": "address", "name": "referrer", "type": "address"}, {"internalType": "uint256", "name": "referralReward", "type": "uint256"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}], "internalType": "struct PackageManagerV2_1.UserPurchase[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getUserPackageCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getUserPackages", "outputs": [{"components": [{"internalType": "uint256", "name": "packageId", "type": "uint256"}, {"internalType": "uint256", "name": "usdtAmount", "type": "uint256"}, {"internalType": "uint256", "name": "totalTokens", "type": "uint256"}, {"internalType": "uint256", "name": "vestTokens", "type": "uint256"}, {"internalType": "uint256", "name": "poolTokens", "type": "uint256"}, {"internalType": "uint256", "name": "lpTokens", "type": "uint256"}, {"internalType": "address", "name": "referrer", "type": "address"}, {"internalType": "uint256", "name": "referralReward", "type": "uint256"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}], "internalType": "struct PackageManagerV2_1.UserPurchase[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "index", "type": "uint256"}], "name": "getUserPurchase", "outputs": [{"components": [{"internalType": "uint256", "name": "packageId", "type": "uint256"}, {"internalType": "uint256", "name": "usdtAmount", "type": "uint256"}, {"internalType": "uint256", "name": "totalTokens", "type": "uint256"}, {"internalType": "uint256", "name": "vestTokens", "type": "uint256"}, {"internalType": "uint256", "name": "poolTokens", "type": "uint256"}, {"internalType": "uint256", "name": "lpTokens", "type": "uint256"}, {"internalType": "address", "name": "referrer", "type": "address"}, {"internalType": "uint256", "name": "referralReward", "type": "uint256"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}], "internalType": "struct PackageManagerV2_1.UserPurchase", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getUserPurchaseCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getUserPurchases", "outputs": [{"components": [{"internalType": "uint256", "name": "packageId", "type": "uint256"}, {"internalType": "uint256", "name": "usdtAmount", "type": "uint256"}, {"internalType": "uint256", "name": "totalTokens", "type": "uint256"}, {"internalType": "uint256", "name": "vestTokens", "type": "uint256"}, {"internalType": "uint256", "name": "poolTokens", "type": "uint256"}, {"internalType": "uint256", "name": "lpTokens", "type": "uint256"}, {"internalType": "address", "name": "referrer", "type": "address"}, {"internalType": "uint256", "name": "referralReward", "type": "uint256"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}], "internalType": "struct PackageManagerV2_1.UserPurchase[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getUserRedemptionCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getUserRedemptions", "outputs": [{"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "timestamps", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getUserStats", "outputs": [{"components": [{"internalType": "uint256", "name": "totalInvested", "type": "uint256"}, {"internalType": "uint256", "name": "totalTokensReceived", "type": "uint256"}, {"internalType": "uint256", "name": "totalVestTokens", "type": "uint256"}, {"internalType": "uint256", "name": "totalPoolTokens", "type": "uint256"}, {"internalType": "uint256", "name": "totalLPTokens", "type": "uint256"}, {"internalType": "uint256", "name": "totalReferralRewards", "type": "uint256"}, {"internalType": "uint256", "name": "purchaseCount", "type": "uint256"}, {"internalType": "uint256", "name": "redemptionCount", "type": "uint256"}, {"internalType": "uint256", "name": "totalRedemptions", "type": "uint256"}], "internalType": "struct PackageManagerV2_1.UserStats", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "lpToken", "outputs": [{"internalType": "contract ILPToken", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "nextPackageId", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "address", "name": "referrer", "type": "address"}], "name": "purchase", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "lpAmount", "type": "uint256"}], "name": "redeem", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "router", "outputs": [{"internalType": "contract IPancakeRouter", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newWindow", "type": "uint256"}], "name": "setDeadlineWindow", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newTreasury", "type": "address"}], "name": "setTreasury", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "shareToken", "outputs": [{"internalType": "contract IShareToken", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "taxManager", "outputs": [{"internalType": "contract ISwapTaxManager", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}], "name": "togglePackage", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "treasury", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "usdt", "outputs": [{"internalType": "contract IERC20Decimals", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "vesting<PERSON><PERSON>", "outputs": [{"internalType": "contract IVestingVault", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}
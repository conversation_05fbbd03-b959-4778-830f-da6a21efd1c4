{"_format": "hh-sol-artifact-1", "contractName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sourceName": "contracts/PackageManagerV2_1.sol", "abi": [{"inputs": [], "name": "claim", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint64", "name": "cliff", "type": "uint64"}, {"internalType": "uint64", "name": "duration", "type": "uint64"}], "name": "lock", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}
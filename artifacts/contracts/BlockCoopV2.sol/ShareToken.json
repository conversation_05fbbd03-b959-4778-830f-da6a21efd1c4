{"_format": "hh-sol-artifact-1", "contractName": "ShareToken", "sourceName": "contracts/BlockCoopV2.sol", "abi": [{"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "address", "name": "admin", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientAllowance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC20InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC20InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC20InvalidSender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "name": "ERC20InvalidSpender", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MINTER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "mint", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}